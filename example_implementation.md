# 核心文件实现示例

## 1. 主程序入口 (cmd/server/main.go)

```go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "zhengshi/internal/app/router"
    "zhengshi/internal/infrastructure/config"
    "zhengshi/internal/infrastructure/database/mysql"
    "zhengshi/internal/infrastructure/database/redis"
    "zhengshi/internal/pkg/logger"
)

func main() {
    // 加载配置
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // 初始化日志
    logger.Init(cfg.Log)

    // 初始化数据库连接
    db, err := mysql.NewConnection(cfg.Database.MySQL)
    if err != nil {
        log.Fatal("Failed to connect to MySQL:", err)
    }
    defer db.Close()

    // 初始化 Redis 连接
    rdb, err := redis.NewConnection(cfg.Database.Redis)
    if err != nil {
        log.Fatal("Failed to connect to Redis:", err)
    }
    defer rdb.Close()

    // 设置 Gin 模式
    if cfg.Server.Mode == "production" {
        gin.SetMode(gin.ReleaseMode)
    }

    // 初始化路由
    r := router.NewRouter(db, rdb, cfg)

    // 创建 HTTP 服务器
    srv := &http.Server{
        Addr:    ":" + cfg.Server.Port,
        Handler: r,
    }

    // 启动服务器
    go func() {
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatal("Failed to start server:", err)
        }
    }()

    // 优雅关闭
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    if err := srv.Shutdown(ctx); err != nil {
        log.Fatal("Server forced to shutdown:", err)
    }
}
```

## 2. 配置管理 (internal/infrastructure/config/config.go)

```go
package config

import (
    "fmt"
    "os"

    "gopkg.in/yaml.v3"
)

type Config struct {
    Server   ServerConfig   `yaml:"server"`
    Database DatabaseConfig `yaml:"database"`
    AI       AIConfig       `yaml:"ai"`
    Log      LogConfig      `yaml:"log"`
    Redis    RedisConfig    `yaml:"redis"`
}

type ServerConfig struct {
    Port string `yaml:"port"`
    Mode string `yaml:"mode"`
}

type DatabaseConfig struct {
    MySQL MySQLConfig `yaml:"mysql"`
    Redis RedisConfig `yaml:"redis"`
}

type MySQLConfig struct {
    Host     string `yaml:"host"`
    Port     int    `yaml:"port"`
    Username string `yaml:"username"`
    Password string `yaml:"password"`
    Database string `yaml:"database"`
}

type RedisConfig struct {
    Host     string `yaml:"host"`
    Port     int    `yaml:"port"`
    Password string `yaml:"password"`
    DB       int    `yaml:"db"`
}

type AIConfig struct {
    Qwen     QwenConfig     `yaml:"qwen"`
    DeepSeek DeepSeekConfig `yaml:"deepseek"`
}

type QwenConfig struct {
    APIKey  string `yaml:"api_key"`
    BaseURL string `yaml:"base_url"`
}

type DeepSeekConfig struct {
    APIKey  string `yaml:"api_key"`
    BaseURL string `yaml:"base_url"`
}

type LogConfig struct {
    Level  string `yaml:"level"`
    Format string `yaml:"format"`
    Output string `yaml:"output"`
}

func Load() (*Config, error) {
    env := os.Getenv("GO_ENV")
    if env == "" {
        env = "dev"
    }

    configFile := fmt.Sprintf("configs/config.%s.yaml", env)
    
    data, err := os.ReadFile(configFile)
    if err != nil {
        return nil, err
    }

    var config Config
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, err
    }

    return &config, nil
}
```

## 3. 用户实体 (internal/domain/entity/user.go)

```go
package entity

import (
    "time"
)

type User struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    Username  string    `json:"username" gorm:"uniqueIndex;not null"`
    Email     string    `json:"email" gorm:"uniqueIndex;not null"`
    Password  string    `json:"-" gorm:"not null"`
    Balance   float64   `json:"balance" gorm:"default:0"`
    Status    int       `json:"status" gorm:"default:1"` // 1:active, 0:inactive
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

type Application struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    UserID      uint      `json:"user_id" gorm:"not null"`
    Name        string    `json:"name" gorm:"not null"`
    Description string    `json:"description"`
    APIKey      string    `json:"api_key" gorm:"uniqueIndex;not null"`
    Status      int       `json:"status" gorm:"default:1"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    User User `json:"user" gorm:"foreignKey:UserID"`
}

type AIRequest struct {
    ID            uint      `json:"id" gorm:"primaryKey"`
    ApplicationID uint      `json:"application_id" gorm:"not null"`
    UserID        uint      `json:"user_id" gorm:"not null"`
    ImageURL      string    `json:"image_url" gorm:"not null"`
    QwenRequest   string    `json:"qwen_request" gorm:"type:text"`
    QwenResponse  string    `json:"qwen_response" gorm:"type:text"`
    DeepSeekRequest  string `json:"deepseek_request" gorm:"type:text"`
    DeepSeekResponse string `json:"deepseek_response" gorm:"type:text"`
    FinalResult   string    `json:"final_result" gorm:"type:text"`
    Cost          float64   `json:"cost" gorm:"default:0"`
    Status        int       `json:"status" gorm:"default:0"` // 0:processing, 1:success, -1:failed
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    
    Application Application `json:"application" gorm:"foreignKey:ApplicationID"`
    User        User        `json:"user" gorm:"foreignKey:UserID"`
}
```

## 4. 错误码定义 (internal/pkg/errors/codes.go)

```go
package errors

const (
    // 通用错误码
    CodeSuccess           = 0
    CodeInternalError     = 10001
    CodeInvalidParams     = 10002
    CodeUnauthorized      = 10003
    CodeForbidden         = 10004
    CodeNotFound          = 10005

    // 用户相关错误码
    CodeUserNotFound      = 20001
    CodeUserExists        = 20002
    CodeInvalidPassword   = 20003
    CodeInsufficientBalance = 20004

    // 应用相关错误码
    CodeAppNotFound       = 30001
    CodeAppExists         = 30002
    CodeInvalidAPIKey     = 30003

    // AI 服务相关错误码
    CodeQwenError         = 40001
    CodeDeepSeekError     = 40002
    CodeImageProcessError = 40003
)

var CodeMessages = map[int]string{
    CodeSuccess:           "成功",
    CodeInternalError:     "内部服务器错误",
    CodeInvalidParams:     "参数错误",
    CodeUnauthorized:      "未授权",
    CodeForbidden:         "禁止访问",
    CodeNotFound:          "资源不存在",
    
    CodeUserNotFound:      "用户不存在",
    CodeUserExists:        "用户已存在",
    CodeInvalidPassword:   "密码错误",
    CodeInsufficientBalance: "余额不足",
    
    CodeAppNotFound:       "应用不存在",
    CodeAppExists:         "应用已存在",
    CodeInvalidAPIKey:     "无效的API密钥",
    
    CodeQwenError:         "Qwen模型调用失败",
    CodeDeepSeekError:     "DeepSeek模型调用失败",
    CodeImageProcessError: "图片处理失败",
}
```

## 5. 统一响应格式 (internal/pkg/response/response.go)

```go
package response

import (
    "net/http"

    "github.com/gin-gonic/gin"
    "zhengshi/internal/pkg/errors"
)

type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func Success(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, Response{
        Code:    errors.CodeSuccess,
        Message: errors.CodeMessages[errors.CodeSuccess],
        Data:    data,
    })
}

func Error(c *gin.Context, code int) {
    message, exists := errors.CodeMessages[code]
    if !exists {
        message = "未知错误"
    }
    
    c.JSON(http.StatusOK, Response{
        Code:    code,
        Message: message,
    })
}

func ErrorWithMessage(c *gin.Context, code int, message string) {
    c.JSON(http.StatusOK, Response{
        Code:    code,
        Message: message,
    })
}
```

这个架构设计具有以下优势：

1. **清晰的分层结构** - 便于维护和扩展
2. **依赖注入** - 提高代码的可测试性
3. **统一的错误处理** - 标准化的错误响应
4. **配置管理** - 支持多环境部署
5. **领域驱动设计** - 业务逻辑清晰分离

您可以基于这个结构开始开发您的项目。需要我为您创建更多具体的实现文件吗？
