# 基于 Gin 框架的标准项目目录结构

```
zhengshi/
├── cmd/                          # 应用程序入口
│   └── server/
│       └── main.go              # 主程序入口
├── internal/                     # 私有应用程序代码
│   ├── app/                     # 应用程序层
│   │   ├── handler/             # HTTP 处理器
│   │   │   ├── auth.go          # 认证相关接口
│   │   │   ├── user.go          # 用户管理接口
│   │   │   ├── application.go   # 应用管理接口
│   │   │   ├── ai.go            # AI 模型调用接口
│   │   │   └── billing.go       # 计费相关接口
│   │   ├── middleware/          # 中间件
│   │   │   ├── auth.go          # 认证中间件
│   │   │   ├── cors.go          # 跨域中间件
│   │   │   ├── logger.go        # 日志中间件
│   │   │   ├── rate_limit.go    # 限流中间件
│   │   │   └── tenant.go        # 租户中间件
│   │   └── router/              # 路由配置
│   │       └── router.go        # 路由注册
│   ├── domain/                  # 领域层
│   │   ├── entity/              # 实体定义
│   │   │   ├── user.go          # 用户实体
│   │   │   ├── application.go   # 应用实体
│   │   │   ├── ai_request.go    # AI请求实体
│   │   │   └── billing.go       # 计费实体
│   │   ├── repository/          # 仓储接口
│   │   │   ├── user.go          # 用户仓储接口
│   │   │   ├── application.go   # 应用仓储接口
│   │   │   ├── ai_request.go    # AI请求仓储接口
│   │   │   └── billing.go       # 计费仓储接口
│   │   └── service/             # 领域服务
│   │       ├── user.go          # 用户服务
│   │       ├── application.go   # 应用服务
│   │       ├── ai.go            # AI服务
│   │       └── billing.go       # 计费服务
│   ├── infrastructure/          # 基础设施层
│   │   ├── database/            # 数据库相关
│   │   │   ├── mysql/           # MySQL 实现
│   │   │   │   ├── connection.go # 数据库连接
│   │   │   │   ├── user.go      # 用户数据访问
│   │   │   │   ├── application.go # 应用数据访问
│   │   │   │   ├── ai_request.go # AI请求数据访问
│   │   │   │   └── billing.go   # 计费数据访问
│   │   │   └── redis/           # Redis 实现
│   │   │       ├── connection.go # Redis连接
│   │   │       └── cache.go     # 缓存操作
│   │   ├── external/            # 外部服务
│   │   │   ├── qwen/            # Qwen 模型客户端
│   │   │   │   └── client.go
│   │   │   └── deepseek/        # DeepSeek 模型客户端
│   │   │       └── client.go
│   │   └── config/              # 配置管理
│   │       └── config.go        # 配置结构和加载
│   └── pkg/                     # 内部共享包
│       ├── logger/              # 日志包
│       │   └── logger.go
│       ├── errors/              # 错误处理
│       │   ├── codes.go         # 错误码定义
│       │   └── errors.go        # 错误处理
│       ├── response/            # 响应格式
│       │   └── response.go      # 统一响应格式
│       ├── utils/               # 工具函数
│       │   ├── crypto.go        # 加密工具
│       │   ├── validator.go     # 验证工具
│       │   └── time.go          # 时间工具
│       └── constants/           # 常量定义
│           └── constants.go
├── api/                         # API 文档和定义
│   ├── openapi/                 # OpenAPI 规范
│   │   └── api.yaml
│   └── proto/                   # Protocol Buffers (如果使用gRPC)
├── configs/                     # 配置文件
│   ├── config.yaml              # 主配置文件
│   ├── config.dev.yaml          # 开发环境配置
│   ├── config.prod.yaml         # 生产环境配置
│   └── config.test.yaml         # 测试环境配置
├── scripts/                     # 脚本文件
│   ├── build.sh                 # 构建脚本
│   ├── deploy.sh                # 部署脚本
│   └── migrate.sh               # 数据库迁移脚本
├── migrations/                  # 数据库迁移文件
│   ├── 001_create_users_table.up.sql
│   ├── 001_create_users_table.down.sql
│   ├── 002_create_applications_table.up.sql
│   └── 002_create_applications_table.down.sql
├── test/                        # 测试文件
│   ├── integration/             # 集成测试
│   ├── unit/                    # 单元测试
│   └── fixtures/                # 测试数据
├── docs/                        # 文档
│   ├── README.md                # 项目说明
│   ├── API.md                   # API 文档
│   └── DEPLOYMENT.md            # 部署文档
├── docker/                      # Docker 相关
│   ├── Dockerfile               # Docker 镜像构建
│   └── docker-compose.yml       # 本地开发环境
├── .gitignore                   # Git 忽略文件
├── go.mod                       # Go 模块文件
├── go.sum                       # Go 依赖校验文件
├── Makefile                     # 构建和管理命令
└── README.md                    # 项目根目录说明
```

## 目录结构说明

### 1. cmd/ - 应用程序入口
- 包含应用程序的主入口点
- 每个可执行程序都有自己的子目录

### 2. internal/ - 私有应用程序代码
- **app/**: 应用程序层，处理 HTTP 请求和响应
- **domain/**: 领域层，包含业务逻辑和规则
- **infrastructure/**: 基础设施层，处理外部依赖
- **pkg/**: 内部共享包，可被项目内部复用

### 3. 其他重要目录
- **api/**: API 文档和定义
- **configs/**: 配置文件管理
- **migrations/**: 数据库迁移文件
- **test/**: 测试相关文件
- **docs/**: 项目文档

## 架构特点

1. **清晰的分层架构**: 遵循 DDD (领域驱动设计) 原则
2. **依赖注入**: 便于测试和维护
3. **配置管理**: 支持多环境配置
4. **错误处理**: 统一的错误码和响应格式
5. **日志系统**: 结构化日志记录
6. **测试友好**: 清晰的测试目录结构
